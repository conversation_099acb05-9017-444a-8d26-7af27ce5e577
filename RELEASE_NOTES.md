# SupplyLine MRO Suite Release Notes

## Version 3.5.4 (Upcoming)

### Bug Fixes
- Fixed issue #4: Add New Tool functionality not working
  - Tools can now be successfully added through the UI
  - Added success message when a tool is created
  - Improved error handling for tool creation
  - Fixed backend API to return complete tool data

## Version 3.5.2 (Current)

### Features
- Added calibration management for tools
- Improved chemical inventory tracking
- Enhanced reporting capabilities

### Bug Fixes
- Fixed issue with checkout history not displaying correctly
- Resolved authentication issues for some user roles
- Improved error handling for network failures

## Version 3.5.1

### Features
- Added barcode generation for chemicals
- Implemented expiration date tracking for chemicals
- Added reorder notifications for low stock items

### Bug Fixes
- Fixed search functionality in tools list
- Resolved issue with user permissions for tool checkout
- Fixed date formatting in reports

## Version 3.5.0

### Major Features
- Complete UI redesign with improved user experience
- Added chemical inventory management
- Implemented tool service history tracking
- Added comprehensive reporting system
- Improved user management with role-based permissions

### Bug Fixes
- Multiple performance improvements
- Enhanced security for user authentication
- Fixed various UI inconsistencies
