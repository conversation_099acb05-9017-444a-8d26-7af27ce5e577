# Google Cloud Platform Environment Configuration
# Copy this file to .env.gcp and update the values for your deployment

# Google Cloud Project Configuration
# REPLACE 'your-gcp-project-id' with your actual Google Cloud Project ID
PROJECT_ID=gen-lang-client-0819985982
REGION=us-west1
ENVIRONMENT=production

# Backend Environment Variables (Cloud Run)
FLASK_ENV=production
SECRET_KEY=your-production-secret-key-32-chars-min
CORS_ORIGINS=https://supplyline-frontend-production-454313121816.us-west1.run.app

# Database Configuration (Cloud SQL)
DB_HOST=/cloudsql/gen-lang-client-0819985982:us-west1:supplyline-db
DB_USER=supplyline_user
DB_PASSWORD=your-secure-database-password
DB_NAME=supplyline

# Session Configuration
SESSION_VALIDATE_IP=false
PYTHONDONTWRITEBYTECODE=1
PYTHONUNBUFFERED=1

# Security Configuration
MAX_FAILED_ATTEMPTS=5
INITIAL_LOCKOUT_MINUTES=15
LOCKOUT_MULTIPLIER=2
MAX_LOCKOUT_MINUTES=60

# Frontend Environment Variables (Note: These are now configured statically in cloudbuild.yaml)
# VITE_API_URL is set during build time via Cloud Build substitution variables
# For local development, use: VITE_API_URL=http://localhost:5000

# Cloud Build Substitution Variables (Static Configuration)
# These URLs are now statically configured to prevent deployment issues
_ENVIRONMENT=production
_REGION=us-west1
_SECRET_KEY=your-production-secret-key
_CLOUDSQL_INSTANCE=gen-lang-client-0819985982:us-west1:supplyline-db
_BACKEND_URL=https://supplyline-backend-production-454313121816.us-west1.run.app
_FRONTEND_URL=https://supplyline-frontend-production-454313121816.us-west1.run.app

# Resource Limits (Cloud Run)
BACKEND_CPU_LIMIT=1
BACKEND_MEMORY_LIMIT=1Gi
FRONTEND_CPU_LIMIT=0.5
FRONTEND_MEMORY_LIMIT=512Mi

# Monitoring and Logging
ENABLE_CLOUD_LOGGING=true
LOG_LEVEL=INFO

# Backup Configuration
BACKUP_SCHEDULE=daily
BACKUP_RETENTION_DAYS=30
