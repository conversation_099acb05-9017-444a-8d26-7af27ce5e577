# Playwright E2E Testing Environment Configuration
# Copy this file to .env.local and update values as needed

# Application URLs
PLAYWRIGHT_BASE_URL=http://localhost:5173
FRONTEND_URL=http://localhost:5173
BACKEND_URL=http://localhost:5000

# Test User Credentials
# Admin User
E2E_ADMIN_EMPLOYEE_NUMBER=ADMIN001
E2E_ADMIN_PASSWORD=admin123

# Regular User (optional - tests will be skipped if not provided)
E2E_USER_EMPLOYEE_NUMBER=USER001
E2E_USER_PASSWORD=user123

# Test Configuration
E2E_TIMEOUT=30000
E2E_RETRY_COUNT=2
E2E_PARALLEL_WORKERS=1

# Browser Configuration
E2E_HEADLESS=true
E2E_SLOW_MO=0

# Test Data
E2E_TEST_DATABASE=test_supplyline
E2E_CLEANUP_AFTER_TESTS=true

# CI/CD Configuration
CI=false
E2E_GENERATE_REPORT=true
E2E_RECORD_VIDEO=retain-on-failure
E2E_TAKE_SCREENSHOTS=only-on-failure
