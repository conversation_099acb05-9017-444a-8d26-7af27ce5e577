FROM python:3.11-slim

# Install PostgreSQL client and dependencies
RUN apt-get update && apt-get install -y \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
RUN pip install psycopg2-binary

# Copy the initialization script
COPY db_init_job.py /app/db_init_job.py

# Set working directory
WORKDIR /app

# Make script executable
RUN chmod +x db_init_job.py

# Run the initialization script
CMD ["python", "db_init_job.py"]
